/**
 * Module Federation 2.0 配置示例
 * 这个文件展示了如何在项目中配置和使用 Module Federation 2.0
 */

const { createOptimizedMFConfig } = require('./src/bin/common/config/moduleFederationRuntime');

// 基础配置
const baseConfig = {
  // 应用名称，必须唯一
  name: 'zknow-boot',
  
  // 端口号
  port: 3000,
  
  // 构建模式
  mode: 'development', // 或 'production'
  
  // 暴露的模块
  exposes: {
    './App': './src/App',
    './components/Button': './src/components/Button',
    './utils': './src/utils/index',
  },
  
  // 远程模块配置
  remotes: {
    // 示例：消费其他微前端应用
    // 'remote-app': 'remoteApp@http://localhost:3001/mf-manifest.json',
    // 'shared-components': 'sharedComponents@http://localhost:3002/mf-manifest.json',
  },
  
  // 共享依赖配置
  shared: {
    // React 生态
    react: {
      singleton: true,
      requiredVersion: '^16.14.0',
      eager: false, // 延迟加载以提高性能
    },
    'react-dom': {
      singleton: true,
      requiredVersion: '^16.14.0',
      eager: false,
    },
    
    // 状态管理
    mobx: {
      singleton: true,
      requiredVersion: '~4.15.7',
    },
    'mobx-react': {
      singleton: true,
      requiredVersion: '~6.1.1',
    },
    
    // 路由
    'react-router': {
      singleton: true,
      requiredVersion: '^5.3.4',
    },
    'react-router-dom': {
      singleton: true,
      requiredVersion: '^5.3.4',
    },
    
    // 工具库
    lodash: {
      singleton: true,
      requiredVersion: '^4.17.5',
    },
    moment: {
      singleton: true,
      requiredVersion: '2.24.0',
    },
    axios: {
      singleton: true,
      requiredVersion: '>=0.19.0 <= 0.19.2',
    },
    
    // UI 组件库
    'choerodon-ui': {
      singleton: true,
      requiredVersion: false, // 使用任何版本
    },
    
    // 业务组件库
    '@zknow/utils': {
      singleton: true,
      requiredVersion: false,
    },
    '@zknow/components': {
      singleton: true,
      requiredVersion: false,
    },
  },
};

// 创建优化的配置
const optimizedConfig = createOptimizedMFConfig(baseConfig);

// 导出配置
module.exports = optimizedConfig;

// 如果你想要自定义配置，可以这样做：
const customConfig = {
  ...optimizedConfig,
  
  // 添加自定义的运行时插件
  runtimePlugins: [
    // './src/runtime-plugins/custom-plugin.js',
  ],
  
  // 实验性功能
  experiments: {
    federationRuntime: 'hoisted',
  },
  
  // 开发模式配置
  dev: {
    port: baseConfig.port,
    host: 'localhost',
  },
};

// 使用示例：
/*
// 在你的 webpack.config.js 中：
const { ModuleFederationPlugin } = require('@module-federation/enhanced/webpack');
const mfConfig = require('./module-federation.config.js');

module.exports = {
  // ... 其他 webpack 配置
  plugins: [
    new ModuleFederationPlugin(mfConfig),
    // ... 其他插件
  ],
};

// 在你的应用入口文件中初始化运行时：
import { initializeFederation } from './src/bin/common/runtime/federationInit';

// 初始化 Module Federation
initializeFederation({
  name: 'zknow-boot',
  remotes: [
    // { name: 'remote-app', entry: 'http://localhost:3001/mf-manifest.json' },
  ],
  debug: process.env.NODE_ENV === 'development',
});

// 动态加载远程模块：
import { loadRemoteModule } from './src/bin/common/runtime/federationInit';

const RemoteComponent = React.lazy(() => 
  loadRemoteModule('remote-app', './Component')
);
*/
