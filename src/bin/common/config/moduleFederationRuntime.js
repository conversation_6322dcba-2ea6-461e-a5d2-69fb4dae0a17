/**
 * Module Federation 2.0 Runtime 配置
 * 提供运行时动态加载和共享依赖管理
 */

// 运行时插件配置
export const runtimePlugins = [
  // 可以在这里添加自定义的运行时插件
];

/**
 * 获取运行时共享依赖配置
 * @param {Object} sharedModules - 基础共享模块
 * @returns {Object} 运行时共享配置
 */
export function getRuntimeSharedConfig(sharedModules) {
  return {
    // 核心 React 生态
    react: {
      singleton: true,
      requiredVersion: sharedModules.react,
      eager: false, // 延迟加载以提高性能
    },
    'react-dom': {
      singleton: true,
      requiredVersion: sharedModules['react-dom'],
      eager: false,
    },
    
    // 状态管理
    mobx: {
      singleton: true,
      requiredVersion: sharedModules.mobx,
    },
    'mobx-react': {
      singleton: true,
      requiredVersion: sharedModules['mobx-react'],
    },
    
    // 工具库
    lodash: {
      singleton: true,
      requiredVersion: sharedModules.lodash,
    },
    moment: {
      singleton: true,
      requiredVersion: sharedModules.moment,
    },
    axios: {
      singleton: true,
      requiredVersion: sharedModules.axios,
    },
    
    // 路由
    'react-router': {
      singleton: true,
      requiredVersion: sharedModules['react-router'],
    },
    'react-router-dom': {
      singleton: true,
      requiredVersion: sharedModules['react-router-dom'],
    },
  };
}

/**
 * 获取 MF 2.0 增强配置
 * @param {string} routeName - 路由名称
 * @param {number} port - 端口号
 * @param {string} mode - 构建模式
 * @returns {Object} 增强配置
 */
export function getEnhancedConfig(routeName, port, mode) {
  return {
    // 启用 manifest 功能
    manifest: true,
    
    // 开发模式下的热更新配置
    dev: mode === 'start' ? {
      port,
      host: 'localhost',
    } : undefined,
    
    // 运行时插件
    runtimePlugins,
    
    // 实验性功能
    experiments: {
      // 启用动态类型提示
      federationRuntime: 'hoisted',
    },
  };
}

/**
 * 创建优化的 Module Federation 配置
 * @param {Object} options - 配置选项
 * @returns {Object} 完整的 MF 配置
 */
export function createOptimizedMFConfig(options) {
  const {
    routeName,
    exposes,
    shared,
    port,
    mode,
    remotes = {},
  } = options;

  return {
    name: routeName,
    filename: 'mf-manifest.json',
    exposes,
    remotes,
    shared: {
      ...getRuntimeSharedConfig(shared),
      // 保留原有的自定义共享配置
      ...shared,
    },
    ...getEnhancedConfig(routeName, port, mode),
  };
}

export default {
  runtimePlugins,
  getRuntimeSharedConfig,
  getEnhancedConfig,
  createOptimizedMFConfig,
};
