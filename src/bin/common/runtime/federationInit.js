/**
 * Module Federation 2.0 运行时初始化
 * 提供动态远程模块加载和共享依赖管理
 */

import { init, loadRemote } from '@module-federation/enhanced/runtime';

/**
 * 初始化 Module Federation 运行时
 * @param {Object} options - 初始化选项
 */
export async function initializeFederation(options = {}) {
  const {
    name,
    remotes = [],
    shared = {},
    debug = false,
  } = options;

  try {
    // 初始化 Module Federation 运行时
    await init({
      name,
      remotes,
      shared,
    });

    if (debug) {
      console.log(`[MF Runtime] ${name} initialized successfully`);
      console.log('[MF Runtime] Remotes:', remotes);
    }

    // 设置全局错误处理
    setupErrorHandling(debug);

    return true;
  } catch (error) {
    console.error('[MF Runtime] Initialization failed:', error);
    return false;
  }
}

/**
 * 动态加载远程模块
 * @param {string} remoteName - 远程模块名称
 * @param {string} modulePath - 模块路径
 * @returns {Promise<any>} 加载的模块
 */
export async function loadRemoteModule(remoteName, modulePath = './') {
  try {
    const module = await loadRemote(`${remoteName}${modulePath}`);
    return module;
  } catch (error) {
    console.error(`[MF Runtime] Failed to load remote module ${remoteName}${modulePath}:`, error);
    
    // 返回一个错误边界组件
    return {
      default: () => {
        return React.createElement('div', {
          style: {
            padding: '20px',
            border: '1px solid #ff4d4f',
            borderRadius: '4px',
            backgroundColor: '#fff2f0',
            color: '#ff4d4f',
          }
        }, `Failed to load remote module: ${remoteName}${modulePath}`);
      }
    };
  }
}

/**
 * 预加载远程模块
 * @param {Array} remotes - 要预加载的远程模块列表
 */
export async function preloadRemotes(remotes = []) {
  const preloadPromises = remotes.map(async (remote) => {
    try {
      await loadRemoteModule(remote.name, remote.module || './');
      console.log(`[MF Runtime] Preloaded: ${remote.name}`);
    } catch (error) {
      console.warn(`[MF Runtime] Failed to preload: ${remote.name}`, error);
    }
  });

  await Promise.allSettled(preloadPromises);
}

/**
 * 检查远程模块是否可用
 * @param {string} remoteName - 远程模块名称
 * @returns {Promise<boolean>} 是否可用
 */
export async function checkRemoteAvailability(remoteName) {
  try {
    await loadRemoteModule(remoteName);
    return true;
  } catch (error) {
    return false;
  }
}

/**
 * 设置错误处理
 * @param {boolean} debug - 是否开启调试模式
 */
function setupErrorHandling(debug) {
  // 监听模块联邦相关的错误
  window.addEventListener('unhandledrejection', (event) => {
    if (event.reason && event.reason.message && 
        event.reason.message.includes('Loading script failed')) {
      if (debug) {
        console.warn('[MF Runtime] Remote module loading failed:', event.reason);
      }
      // 可以在这里添加重试逻辑或降级处理
    }
  });

  // 监听网络错误
  window.addEventListener('error', (event) => {
    if (event.target && event.target.tagName === 'SCRIPT') {
      if (debug) {
        console.warn('[MF Runtime] Script loading error:', event.target.src);
      }
    }
  });
}

/**
 * 获取已加载的远程模块信息
 * @returns {Array} 远程模块信息列表
 */
export function getLoadedRemotes() {
  const federation = window.__FEDERATION__;
  if (federation && federation.__INSTANCES__) {
    return federation.__INSTANCES__.map(instance => ({
      name: instance.name,
      version: instance.version,
    }));
  }
  return [];
}

/**
 * 清理模块联邦缓存
 */
export function clearFederationCache() {
  if (window.__FEDERATION__) {
    window.__FEDERATION__.__GLOBAL_LOADING_REMOTE_ENTRY__ = {};
  }
}

export default {
  initializeFederation,
  loadRemoteModule,
  preloadRemotes,
  checkRemoteAvailability,
  getLoadedRemotes,
  clearFederationCache,
};
