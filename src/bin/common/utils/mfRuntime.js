/**
 * Module Federation 2.0 运行时工具
 * 简化版本，提供基础的动态加载功能
 */

/**
 * 动态加载远程模块
 * @param {string} remoteName - 远程模块名称
 * @param {string} modulePath - 模块路径
 * @returns {Promise<any>} 加载的模块
 */
export async function loadRemoteModule(remoteName, modulePath = './') {
  try {
    // 检查是否支持动态导入
    if (typeof __webpack_require__ !== 'undefined' && __webpack_require__.federation) {
      // 使用 webpack 5 的模块联邦 API
      const module = await __webpack_require__.federation.loadRemote(`${remoteName}${modulePath}`);
      return module;
    }
    
    // 降级到传统的动态导入
    const module = await import(/* webpackIgnore: true */ `${remoteName}${modulePath}`);
    return module;
  } catch (error) {
    console.error(`[MF Runtime] Failed to load remote module ${remoteName}${modulePath}:`, error);
    
    // 返回一个错误边界组件
    return {
      default: () => {
        if (typeof React !== 'undefined') {
          return React.createElement('div', {
            style: {
              padding: '20px',
              border: '1px solid #ff4d4f',
              borderRadius: '4px',
              backgroundColor: '#fff2f0',
              color: '#ff4d4f',
              textAlign: 'center',
            }
          }, `模块加载失败: ${remoteName}${modulePath}`);
        }
        return null;
      }
    };
  }
}

/**
 * 检查远程模块是否可用
 * @param {string} remoteName - 远程模块名称
 * @returns {Promise<boolean>} 是否可用
 */
export async function checkRemoteAvailability(remoteName) {
  try {
    await loadRemoteModule(remoteName);
    return true;
  } catch (error) {
    return false;
  }
}

/**
 * 获取已加载的远程模块信息
 * @returns {Array} 远程模块信息列表
 */
export function getLoadedRemotes() {
  if (typeof window !== 'undefined' && window.__FEDERATION__) {
    const federation = window.__FEDERATION__;
    if (federation.__INSTANCES__) {
      return federation.__INSTANCES__.map(instance => ({
        name: instance.name,
        version: instance.version || 'unknown',
      }));
    }
  }
  return [];
}

/**
 * 清理模块联邦缓存
 */
export function clearFederationCache() {
  if (typeof window !== 'undefined' && window.__FEDERATION__) {
    window.__FEDERATION__.__GLOBAL_LOADING_REMOTE_ENTRY__ = {};
  }
}

/**
 * 设置模块联邦错误处理
 * @param {boolean} debug - 是否开启调试模式
 */
export function setupMFErrorHandling(debug = false) {
  if (typeof window === 'undefined') return;

  // 监听模块联邦相关的错误
  window.addEventListener('unhandledrejection', (event) => {
    if (event.reason && event.reason.message && 
        event.reason.message.includes('Loading script failed')) {
      if (debug) {
        console.warn('[MF Runtime] Remote module loading failed:', event.reason);
      }
      // 可以在这里添加重试逻辑或降级处理
    }
  });

  // 监听网络错误
  window.addEventListener('error', (event) => {
    if (event.target && event.target.tagName === 'SCRIPT') {
      if (debug) {
        console.warn('[MF Runtime] Script loading error:', event.target.src);
      }
    }
  });
}

/**
 * 初始化模块联邦运行时
 * @param {Object} options - 初始化选项
 */
export function initMFRuntime(options = {}) {
  const { debug = false } = options;
  
  // 设置错误处理
  setupMFErrorHandling(debug);
  
  if (debug) {
    console.log('[MF Runtime] Module Federation 2.0 runtime initialized');
  }
}

export default {
  loadRemoteModule,
  checkRemoteAvailability,
  getLoadedRemotes,
  clearFederationCache,
  setupMFErrorHandling,
  initMFRuntime,
};
